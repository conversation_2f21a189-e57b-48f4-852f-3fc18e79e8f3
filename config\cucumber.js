module.exports = {
    default: {
        tags: process.env.npm_config_TAGS || process.env.TAGS || "",
        formatOptions: {
            snippetInterface: "async-await"
        },
        paths: [
            "src/test/features/"
        ],
        publishQuiet: true,
        dryRun: false,
        require: [
            "src/test/steps/*.ts",
            "src/hooks/hooks.ts"
        ],
        requireModule: [
            "ts-node/register"
        ],
        format: [
            "progress-bar",
            "html:test-results/cucumber-report.html",
            "json:test-results/cucumber-report.json",
            "rerun:@rerun.txt"
        ],
        parallel: 1
    },
    rerun: {
        formatOptions: {
            snippetInterface: "async-await"
        },
        // Remove paths to avoid running all feature files
        publishQuiet: true,
        dryRun: false,
        require: [
            "src/test/steps/*.ts",
            "src/hooks/hooks.ts"
        ],
        requireModule: [
            "ts-node/register"
        ],
        format: [
            "progress-bar",
            "html:test-results/cucumber-report-rerun.html", // Use separate file to avoid overwriting
            "json:test-results/cucumber-report-rerun.json", // Use separate file to avoid overwriting
            "rerun:@rerun.txt"
        ],
        parallel: 1 // Keep at 1 to avoid parallel execution issues
    }
};