{"cookies": [{"name": "_csrf", "value": "2QuFGzu99UWvcwdKlK_GU6JF", "domain": "cpsdev.us.auth0.com", "path": "/usernamepassword/login", "expires": 1758807399.44958, "httpOnly": true, "secure": true, "sameSite": "Lax"}, {"name": "ai_user", "value": "xk5LteqbZPb0nCOicqy6Zg|2025-09-15T13:36:37.248Z", "domain": "sqa-app.ecps.ca", "path": "/", "expires": 1789479397.250316, "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "did_compat", "value": "s%3Av0%3A6db4a318-6dc4-4e0f-aa8f-81ba41ed8cf5.OQKTuxiC5Joi3p9ZrXQO2tOcI%2FW3M0Wmz24BchkbfM4", "domain": "cpsdev.us.auth0.com", "path": "/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "Lax"}, {"name": "did", "value": "s%3Av0%3Ad709279c-0a55-4b54-8451-3e53c0ece4d4.Sx%2BTRGZR1aUsglq%2FW%2BXloyILt4NpkliUOQNIuWhJV0g", "domain": "cpsdev.us.auth0.com", "path": "/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "auth0", "value": "s%3A7NtGCltlDrwOWIjjVOnvhwKpyMcEq9di.YqXn1z%2FMGYfRRIujCRiMdN8x8bWPGsf76tqjQmsUwfo", "domain": "cpsdev.us.auth0.com", "path": "/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "auth0_compat", "value": "s%3A7NtGCltlDrwOWIjjVOnvhwKpyMcEq9di.YqXn1z%2FMGYfRRIujCRiMdN8x8bWPGsf76tqjQmsUwfo", "domain": "cpsdev.us.auth0.com", "path": "/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "Lax"}, {"name": "ai_session", "value": "Pt99Hy85cX58quotp/imiV|1757943397983|1757943421624", "domain": "sqa-app.ecps.ca", "path": "/", "expires": 1757945221.625517, "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_legacy_auth0.SgHnwewE6BB3pgB4UXbF93el2rLf4Edf.is.authenticated", "value": "true", "domain": "sqa-app.ecps.ca", "path": "/", "expires": **********, "httpOnly": false, "secure": true, "sameSite": "Lax"}, {"name": "auth0.SgHnwewE6BB3pgB4UXbF93el2rLf4Edf.is.authenticated", "value": "true", "domain": "sqa-app.ecps.ca", "path": "/", "expires": **********, "httpOnly": false, "secure": true, "sameSite": "None"}], "origins": [{"origin": "https://sqa-app.ecps.ca", "localStorage": [{"name": "loglevel", "value": "WARN"}, {"name": "@@auth0spajs@@::SgHnwewE6BB3pgB4UXbF93el2rLf4Edf::https://administrationportal-sqa/api::openid profile email", "value": "{\"body\":{\"access_token\":\"****************************************************************************.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"scope\":\"openid profile email\",\"expires_in\":86400,\"token_type\":\"Bearer\",\"audience\":\"https://administrationportal-sqa/api\",\"oauthTokenScope\":\"openid profile email\",\"client_id\":\"SgHnwewE6BB3pgB4UXbF93el2rLf4Edf\"},\"expiresAt\":**********}"}, {"name": "@@auth0spajs@@::SgHnwewE6BB3pgB4UXbF93el2rLf4Edf::@@user@@", "value": "{\"id_token\":\"****************************************************************************.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"decodedToken\":{\"encoded\":{\"header\":\"****************************************************************************\",\"payload\":\"**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"signature\":\"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************\"},\"header\":{\"alg\":\"RS256\",\"typ\":\"JWT\",\"kid\":\"mx-ZOP-FrpjeZhjFR3g-X\"},\"claims\":{\"__raw\":\"****************************************************************************.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"sAMAccountName\":\"********\",\"given_name\":\"cpsadmin1\",\"family_name\":\"cpsadmin1\",\"nickname\":\"********\",\"name\":\"cpsadmin1\",\"picture\":\"https://s.gravatar.com/avatar/ec7f5103418fd8fbe71256ef2972a783?s=480&r=pg&d=https%3A%2F%2Fcdn.auth0.com%2Favatars%2Fcp.png\",\"updated_at\":\"2025-09-15T13:36:57.612Z\",\"email\":\"<EMAIL>\",\"iss\":\"https://cpsdev.us.auth0.com/\",\"aud\":\"SgHnwewE6BB3pgB4UXbF93el2rLf4Edf\",\"sub\":\"ad|AD-arahlthcare|cae9359b-e442-494e-ad04-046fa433ad97\",\"iat\":**********,\"exp\":**********,\"sid\":\"vhEovuAnq96VXJKb8MooJtJqWVzxFAzq\",\"nonce\":\"RmpCS3pxdVFiai1SSUZ2aEVmM1liVXB3ZmxwSUlXcnFfVERIWGRHOERwZQ==\"},\"user\":{\"sAMAccountName\":\"********\",\"given_name\":\"cpsadmin1\",\"family_name\":\"cpsadmin1\",\"nickname\":\"********\",\"name\":\"cpsadmin1\",\"picture\":\"https://s.gravatar.com/avatar/ec7f5103418fd8fbe71256ef2972a783?s=480&r=pg&d=https%3A%2F%2Fcdn.auth0.com%2Favatars%2Fcp.png\",\"updated_at\":\"2025-09-15T13:36:57.612Z\",\"email\":\"<EMAIL>\",\"sub\":\"ad|AD-arahlthcare|cae9359b-e442-494e-ad04-046fa433ad97\"}}}"}]}]}