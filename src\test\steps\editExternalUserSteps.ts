import { Given, When, Then } from "@cucumber/cucumber";
import { fixture } from "../../hooks/pageFixture";
import Assert from "../../helper/wrapper/assert";
import RegisterPage from "../../pages/registerPage";
import UserHomePage from "../../pages/userHomePage";
import EditUserPage from "../../pages/editUserPage";
let registerPage: RegisterPage;
let edituserPage: EditUserPage;
let userHomePage: UserHomePage;
let assert: Assert;

Given(`User navigates to the application with valid super admin credentials`, async function () {
    // [Given] Sets up the initial state of the system.
    registerPage = new RegisterPage(fixture.page);    
   // assert = new Assert(fixture.page);
    //await registerPage.navigateToDefaultPage();

        // Skip login if already authenticated (for @auth tagged scenarios)
    const currentUrl = fixture.page.url();
    if (!currentUrl.includes('sqa-app.ecps.ca') || currentUrl.includes('auth0.com')) {
        await registerPage.navigateToDefaultPage();
    } else {
        // Just navigate to the base URL since we're already authenticated
        await fixture.page.goto(`${process.env.TESTURL}/users`, { waitUntil: "domcontentloaded" });
    }
});


When(`the user navigates to the user page`, async function()  {
    // [When] Describes the action or event that triggers the scenario.
    edituserPage = new EditUserPage(fixture.page);    
    await edituserPage.navigateToUserHome();
});

Then(`User search for an existing user`,async function (){
    // [Then] Describes the expected outcome or result of the scenario.
    userHomePage = new UserHomePage(fixture.page);
    await userHomePage.searchUser();
});

When(`User edit an existing user`, async function  (DataTable) {
    // [When] Describes the action or event that triggers the scenario.
    // <DataTable> argument is detected:
    // - With column headers: use DataTable.rowsHash(), which outputs an object containing key-value pairs for each row (e.g. { key1: value, key2: value }).
    // - With row headers: use DataTable.hashes(), which outputs an array of objects (e.g. [{ key1: value, key2: value }]).

         var rs= DataTable.rows();
         for (const element of rs) {
        //console.log(element[0]);
        var resp = await edituserPage.editUser({
            company: element[0],
            role: element[1],
            phoneType: element[2],
            addressType: element[3],
            thirdParty: element[4],
            functionalArea: element[5]
        });
    }
});