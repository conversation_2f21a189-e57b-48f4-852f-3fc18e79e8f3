Feature: User View my facilty functionality

    @ViewMyFacility @mrun
    Scenario: Super Admin - user should be able to View My facility
        Given User navigates to the application with valid super admin credentials
        When the user navigates to the user page
        And creates a new Role with "View My Facility / Facility Groups" permission with in the module "Facility Management".
        And creates a new user with new role Created above and searches for the newly created user
        And <PERSON>go<PERSON> from the portal
        And login to the portal with new User
        And Create New Facility and capture the details
        And User search for a newly creted facility

