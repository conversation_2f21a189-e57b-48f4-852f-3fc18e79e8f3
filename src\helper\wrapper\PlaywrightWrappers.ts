import { Page, expect } from "@playwright/test";
import { clickElement, fillElement, waitAndClickElement } from "./actions";
import * as data from "../../helper/util/test-data/test_data.json";
import { fixture } from "../../hooks/pageFixture";


export default class PlaywrightWrapper {

    constructor(private page: Page) { }
    private pageOfPageText = this.page.locator('nav[aria-label="Pagination"]>div:nth-child(2) span');
    private showPageText = this.page.locator('nav[aria-label="Pagination"]>div:nth-child(1)');
    private getPageOfPageText(secondTable: boolean = false) {
        const baseSelector = secondTable
            ? 'div[class$="justify-center"] nav[aria-label="Pagination"]'
            : 'nav[aria-label="Pagination"]';

        return this.page.locator(`${baseSelector}>div:nth-child(2) span`);
    }

    private getShowPageText(secondTable: boolean = false) {
        const baseSelector = secondTable
            ? 'div[class$="justify-center"] nav[aria-label="Pagination"]'
            : 'nav[aria-label="Pagination"]';

        return this.page.locator(`${baseSelector}>div:nth-child(1)`);
    }


    private getColumnHeader2 = (index: number) =>
        this.page.locator('div[data-slot="table-container"] thead>tr th').nth(index);
    private getColumnHeader1 = (index: number) =>
        this.page.locator('div[class$="justify-center"] div[data-slot="table-container"] thead>tr th').nth(index);

    private getColumnHeader(index: number, secondTable: boolean = false) {
        const baseSelector = secondTable
            ? 'div[class$="justify-center"] div[data-slot="table-container"]'
            : 'div[data-slot="table-container"]';

        return this.page.locator(`${baseSelector} thead>tr th`).nth(index);
    }

    private getTableData = (index: number) =>
        this.page.locator(`div[data-slot="table-container"] tbody>tr td:nth-child(${index})`);
    private toasterMessOrig = this.page.locator('.Toastify');
    private toasterMess = (str: string) => this.page.locator('.Toastify .Toastify__toast-container .Toastify__toast:has-text("' + str + '")');


    async waitForResponse(url: string): Promise<any> {
        console.log(`Waiting for response containing URL: ${url}`);

        try {
            var resp = await this.page.waitForResponse(
                response => {
                    const responseUrl = response.url();
                    const matches = responseUrl.includes(url) && response.status() >= 200 && response.status() < 299;
                    if (matches) {
                        console.log(`Found matching response: ${responseUrl} with status ${response.status()}`);
                    }
                    return matches;
                },
                { timeout: 30000 }
            );
            return await resp.json();
        } catch (error) {
            console.error(`Timeout waiting for response containing "${url}". Error: ${error.message}`);
            throw error;
        }
    }
    async commonLogin() {
        /* if(await this.page.locator(".loginlink").isVisible()){
             await clickElement(this.page.locator(".loginlink"));
             await fillElement(this.page.locator("#usernameInput"),data.ecps.userName);
             await fillElement(this.page.locator("#passwordInput"),data.ecps.password);
             await clickElement(this.page.locator("#loadingGifDiv"));
             await this.page.waitForLoadState("domcontentloaded");
             await fixture.page.context().storageState({ path:"src/helper/auth/admin.json"});
             await this.goto(process.env.TESTURL);
         }*/
        if (await this.page.locator("div.login-container").isVisible()) {
            //await clickElement(this.page.locator(".login-text"));
            await fillElement(this.page.locator("#username"), data.ecps.userName);
            await fillElement(this.page.locator("#password"), data.ecps.password);
            await this.sleep(1000);
            await clickElement(this.page.locator("#btn-login"));
            await fixture.page.waitForLoadState("domcontentloaded");
            await fixture.page.context().storageState({ path: "src/helper/auth/admin.json" });
            await fixture.page.waitForSelector(".bg-grey", { state: "visible" ,timeout: 50000 });
            //await this.goto(process.env.TESTURL);
        }
    }

    async goto(url: string) {
        await this.page.goto(url, {
            waitUntil: "domcontentloaded"
        });
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    async verifyToast(message: string) {
        fixture.logger.info(await this.toasterMessOrig.allInnerTexts());
        await expect(this.toasterMess(message)).toContainText(message);
    }


    async verifyPaginationText(secondTable?: boolean) {
        const pageOfPageRegex = /^Showing Page \d+ of \d+ Pages$/;
        const showPageRegex = /^Show\s+\d+\s+of\s+\d+\s+records per page$/;
        //const text = await this.pageOfPageText.innerText();
        const pageOfPage = this.getPageOfPageText(secondTable);
        const text = await pageOfPage.innerText();
        expect(text).toEqual(expect.stringMatching(pageOfPageRegex));
        //fixture.logger.info(`Pagination text: ${text} is validated`);
        const showPage = this.getShowPageText(secondTable);
        //const text1 = await this.showPageText.innerText();
        const text1 = await showPage.innerText();
        expect(text1).toEqual(expect.stringMatching(showPageRegex));
        //fixture.logger.info(`Records per page text: ${text} is validated`);
    };

    /**
     * Verifies the count of disabled elements matching a locator.
     * @param expectedCount - The expected number of disabled elements.
     * @param comparison - Optional: "equal" | "greaterThan" | "lessThan" (default: "equal")
     * @param locator - Optional: Locator to find elements. Defaults to disabled inputs in open dropdowns.
     */
    async verifyDisabledElementsCount(
        expectedCount: number,
        elementName: string,
        comparison: "equal" | "greaterThan" | "lessThan" = "equal",
        locator?: any
    ): Promise<void> {
        // Default locator
        const elements = locator ?? this.page.locator('div[data-state="open"] input[data-slot="input"]:disabled');

        try {
            const disabledElementsCount = await elements.count();

            let isValid = false;
            switch (comparison) {
                case "equal":
                    isValid = disabledElementsCount === expectedCount;
                    break;
                case "greaterThan":
                    isValid = disabledElementsCount > expectedCount;
                    break;
                case "lessThan":
                    isValid = disabledElementsCount < expectedCount;
                    break;
            }

            if (!isValid) {
                const message = `${elementName} disabled elements count check failed. Expected ${comparison} ${expectedCount}, but found ${disabledElementsCount}`;
                fixture.logger.error(message);
                throw new Error(message);
            }

            fixture.logger.info(`${elementName} disabled elements count check passed. Found: ${disabledElementsCount}`);
        } catch (error) {
            fixture.logger.error(`${elementName} disabled elements verification failed. Error: ${error}`);
            throw error; // rethrow so test still fails
        }
    }



    async verifyWithLogging(locator: any, elementName: string): Promise<void> {
        try {
            await expect(locator).toBeVisible();
            // fixture.logger.info(`${elementName} is visible`);
        } catch (error) {
            fixture.logger.error(`${elementName} is NOT visible. Error: ${error}`);
            throw error; // rethrow so test still fails
        }
    }

    // async verifyGridColumnNames(expectedHeaders: string[]): Promise<void> {
    //     for (let i = 0; i < expectedHeaders.length; i++) {
    //         const headerElement = this.getColumnHeader(i);
    //         await expect(headerElement).toBeVisible({ timeout: 10000 });
    //         let actualText = (await headerElement.innerText()) || "";
    //         // Only trim leading/trailing spaces (do not collapse internal whitespace)
    //         actualText = actualText.trim();
    //         const expectedText = expectedHeaders[i].trim();
    //         //console.log(`Column ${i + 1}: [${actualText}] vs Expected [${expectedText}]`);
    //         fixture.logger.info(`Column ${i + 1}: [${actualText}] vs Expected [${expectedText}]`);
    //         expect(actualText).toEqual(expectedText); // exact, case-sensitive
    //     }
    // }

    async verifyGridColumnNames(expectedHeaders: string[], withSort: boolean = false, rowLimit: number = 3): Promise<void> {
        for (let i = 0; i < expectedHeaders.length; i++) {
            const headerElement = this.getColumnHeader(i);
            await expect(headerElement).toBeVisible({ timeout: 10000 });

            let actualText = (await headerElement.innerText()) || "";
            actualText = actualText.trim();
            const expectedText = expectedHeaders[i].trim();
            fixture.logger.info(`Column ${i + 1}: [${actualText}] vs Expected [${expectedText}]`);
            expect(actualText).toEqual(expectedText);

            if (withSort) {
                // Skip sorting check for Action, Status, and Month columns
                if (["Action", "Status", "Month", "Total Volume"].includes(expectedText)) {
                    fixture.logger.info(`Skipping sorting check for column: ${expectedText}`);
                    continue;
                }

                await this.getColumnHeader(i + 1).locator('button').click({ force: true });
                await this.sleep(500);
                // Click to sort ascending
                await this.getColumnHeader(i).locator('button').click({ force: true });
                await this.sleep(500);

                // Collect ascending values for up to rowLimit rows
                const beforeSortValues: string[] = [];
                const cells = await this.getTableData(i + 1).all();
                for (let j = 0; j < Math.min(rowLimit, cells.length); j++) {
                    const cellText = (await cells[j].innerText()).trim();
                    if (cellText) beforeSortValues.push(cellText); // Skip empty cells
                }
                fixture.logger.info(`Column ${i + 1}: ascending values (first ${Math.min(rowLimit, cells.length)} rows) ${JSON.stringify(beforeSortValues)}`);

                if (beforeSortValues.length <= 1) {
                    fixture.logger.info(`Column ${i + 1} has <=1 non-empty row, skipping sort check.`);
                    continue;
                }

                // Click again to sort descending
                await this.getColumnHeader(i).locator('button').click({ force: true });
                await this.sleep(500);

                // Collect descending values for up to rowLimit rows
                const afterSortValues: string[] = [];
                for (let j = 0; j < Math.min(rowLimit, cells.length); j++) {
                    const cellText = (await cells[j].innerText()).trim();
                    if (cellText) afterSortValues.push(cellText); // Skip empty cells
                }
                fixture.logger.info(`Column ${i + 1}: descending values (first ${Math.min(rowLimit, cells.length)} rows) ${JSON.stringify(afterSortValues)}`);

                // Natural sorting helper (A → Z, case-insensitive, numeric-aware)
                const isAscending = (arr: string[]) =>
                    arr.every((val, idx) =>
                        idx === 0 ||
                        arr[idx - 1].localeCompare(val, undefined, {
                            numeric: true,
                            sensitivity: "base", // Case-insensitive
                        }) <= 0 // Allow equal values
                    );

                const isDescending = (arr: string[]) =>
                    arr.every((val, idx) =>
                        idx === 0 ||
                        arr[idx - 1].localeCompare(val, undefined, {
                            numeric: true,
                            sensitivity: "base", // Case-insensitive
                        }) >= 0 // Allow equal values
                    );

                // Validate ascending order
                if (!isAscending(beforeSortValues)) {
                    throw new Error(
                        `Column ${i + 1} (${expectedText}) is NOT sorted ascending in first ${Math.min(rowLimit, cells.length)} rows. 
                    Values: ${JSON.stringify(beforeSortValues)}`
                    );
                }
                fixture.logger.info(`Column ${i + 1} (${expectedText}) is sorted in ascending order in first ${Math.min(rowLimit, cells.length)} rows.`);

                // Validate descending order
                if (!isDescending(afterSortValues)) {
                    throw new Error(
                        `Column ${i + 1} (${expectedText}) is NOT sorted descending in first ${Math.min(rowLimit, cells.length)} rows. 
                    Values: ${JSON.stringify(afterSortValues)}`
                    );
                }
                fixture.logger.info(`Column ${i + 1} (${expectedText}) is sorted in descending order in first ${Math.min(rowLimit, cells.length)} rows.`);
            }
        }
    }

    async verifyGridColumnNamesWithSymbols(
        expectedHeaders: string[],
        checkBoxFirst: boolean = false,
        secondTable?: boolean
    ): Promise<void> {
        // Start index: if checkbox is first column, skip it
        const startIndex = checkBoxFirst ? 1 : 0;

        for (let i = 0; i < expectedHeaders.length; i++) {
            const columnIndex = startIndex + i;
            // Select correct header element depending on table
            const headerElement = secondTable
                ? this.getColumnHeader(columnIndex, true)
                : this.getColumnHeader(columnIndex);
            await expect(headerElement).toBeVisible({ timeout: 10000 });

            let actualText = (await headerElement.innerText()) || "";
            actualText = actualText
                .replace(/\s*\n\s*/g, " ")  // Replace line breaks with single space
                .replace(/\s+/g, " ")       // Collapse multiple spaces
                .trim();

            const expectedText = expectedHeaders[i].replace(/\s+/g, " ").trim();
            //console.log(`Column ${columnIndex + 1}: [${actualText}] vs Expected [${expectedText}]`);
            await expect(actualText).toEqual(expectedText); // exact, case-sensitive
            fixture.logger.info(`Column ${columnIndex + 1}: [${actualText}] vs Expected [${expectedText}]`)
        }


    }

    /**
 * Verifies the Unsaved Change popup.
 */

    async verifyunsavedChangePopup() {

        await fixture.page.getByRole('button', { name: 'Go back' }).click();
        await this.verifyWithLogging(
            this.page.getByRole('button', { name: 'Close dialog' }),
            "Unsaved Change popup Button 'Close dialog'"
        );
        await this.verifyWithLogging(
            fixture.page.getByRole('button', { name: 'Yes, Discard changes' }),
            "Unsaved Change popup Button 'Yes Discard changes'"
        );
        await this.verifyWithLogging(
            fixture.page.getByRole('button', { name: 'No, Stay on page' }),
            "Unsaved Change popup Button 'No Stay on page'"
        );
        await expect(fixture.page.getByLabel('Unsaved Changes').locator('span')).toContainText('Unsaved Changes');
        await expect(fixture.page.locator('[role="alertdialog"] [data-slot="alert-dialog-description"]')).toContainText('You have unsaved changes. Do you wish to discard changes?');
        await fixture.page.getByRole('button', { name: 'Close dialog' }).click();

    }


    /**
 * Verifies the reset popup.
 */
    async verifyResetPopup(): Promise<void> {
        await fixture.page.getByRole('button', { name: 'Reset Changes' }).click();
        await this.verifyWithLogging(fixture.page.getByRole('button', { name: 'Close dialog' }), "reset popup Button 'Close dialog'");
        await this.verifyWithLogging(fixture.page.getByRole('button', { name: 'Yes, Proceed' }), "reset popup Button 'Yes, Proceed'");
        await this.verifyWithLogging(fixture.page.getByRole('button', { name: 'No, Cancel' }), "reset popup Button 'No, Cancel'");
        await expect(fixture.page.getByLabel('Reset Changes').locator('span')).toContainText('Reset Changes');
        await expect(fixture.page.locator('[role="alertdialog"] [data-slot="alert-dialog-description"]')).toContainText('Filters, sorting, pagination, and all inputs on this page shall be reset. Please confirm if you would like to reset the changes.');
        await fixture.page.getByRole('button', { name: 'Close dialog' }).click();
    }


    //validate drop down options with or witout 

    async validateDropdownOptions(
        locator: any,
        expectedOptions: string[],
        ignoreOrder: boolean = false
    ): Promise<void> {
        // Click the dropdown to expand
        await locator.click();
        await this.page.waitForTimeout(1000);
        await fixture.page.waitForSelector('div[data-slot="dropdown-menu-item"]');
        // Freshly capture menu items
        const options = await this.page.locator('div[data-slot="dropdown-menu-item"]').all();
        const actualOptions: string[] = [];

        for (const option of options) {
            const text = (await option.innerText()).trim();
            actualOptions.push(text);
            // fixture.logger.info(`captured option: ${text}`);
        }

        // Log the full collected list once
        fixture.logger.info(`final actual options: ${actualOptions.join(",")}`);
        fixture.logger.info(`expected options: ${expectedOptions.join(",")}`);

        // Assert count matches
        expect(actualOptions.length).toBe(expectedOptions.length);

        if (ignoreOrder) {
            // Compare ignoring order
            expect(new Set(actualOptions)).toEqual(new Set(expectedOptions));
        } else {
            // Compare with order
            for (let i = 0; i < expectedOptions.length; i++) {
                expect(actualOptions[i]).toBe(expectedOptions[i]);
            }
        }
    }

    //helper to match with date modified regex
    async asrtTextMatches(locator: any, regex: RegExp): Promise<void> {
        const text = await locator.innerText();
        expect(text).toEqual(expect.stringMatching(regex));
    }

    /**
     * Verifies the user page in either edit or view mode.
     * @param isEdited - Indicates whether the page was edited in the edit page (true) 
     *                   or created in the create page and not yet edited (false).
     */
    async verifyUserCreateorModifydate(isEdited: boolean = false): Promise<void> {
        const textRegex: RegExp = /^[^\s].*$/;
        const hyphenRegex: RegExp = /^-$/;
        const dateRegex: RegExp = /^(0?[1-9]|[12]\d|3[01]) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d{4} ([01]?\d|2[0-3]):[0-5]\d ET$/;

        // Get the date element for logging
        const dateElement: string = await fixture.page.locator("div[class$='gap-1']").filter({ hasText: "Date Created" }).locator("p:not([class*='capital'])").innerText();
        fixture.logger.info(`Date Created: ${dateElement}`);
        // Get the Date Last Modified element for logging
        const dateElement1: string = await fixture.page.locator("div[class$='gap-1']").filter({ hasText: "Date Last Modified" }).locator("p:not([class*='capital'])").innerText();
        fixture.logger.info(`Date Last Modified: ${dateElement1}`);

        // Verify Date Created
        await this.asrtTextMatches(
            fixture.page.locator("div[class$='gap-1']").filter({ hasText: "Date Created" }).locator("p:not([class*='capital'])"),
            dateRegex
        );

        // Verify Created By
        await this.asrtTextMatches(
            fixture.page.locator("div[class$='gap-1']").filter({ hasText: "Created By" }).locator("p:not([class*='capital'])"),
            textRegex
        );

        // Determine regex for Last Modified By and Date Last Modified based on page type
        const lastModifiedRegex: RegExp = isEdited ? textRegex : hyphenRegex;
        const dateLastModifiedRegex: RegExp = isEdited ? dateRegex : hyphenRegex;

        // Verify Last Modified By
        await this.asrtTextMatches(
            fixture.page.locator("div[class$='gap-1']").filter({ hasText: "Last Modified By" }).locator("p:not([class*='capital'])"),
            lastModifiedRegex
        );

        // Verify Date Last Modified
        await this.asrtTextMatches(
            fixture.page.locator("div[class$='gap-1']").filter({ hasText: "Date Last Modified" }).locator("p:not([class*='capital'])"),
            dateLastModifiedRegex
        );

        fixture.logger.info(`View user page verified successfully${isEdited ? " after user updation" : ""}`);
    }


    async waitAndClickLocator(element: any) {
        await element.waitFor({
            state: "visible"
        });
        await element.click();
    }
    async waitAndClick(locator: string) {
        const element = this.page.locator(locator);
        await element.waitFor({
            state: "visible"
        });
        await element.click();
    }

    async waitForSelectorVisible(locator: string, timeout: number = 5000) {
        await this.page.waitForSelector(locator, {
            state: 'visible',
            timeout
        });
    }

    async escapeOnDropdown() {
        await fixture.page.locator('html').press('Escape');
    }

    async navigateTo(link: string) {
        await Promise.all([
            this.page.waitForNavigation(),
            this.page.click(link)
        ])
    }

}